//  Velvete Store
//
//  Created by <PERSON><PERSON>.
//  2025, Velvete Ltd. All rights reserved.
//
//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/checkout_session.dart';
import '/app/models/customer_address.dart';
import '/app/models/customer_country.dart';
import '/resources/widgets/customer_address_input.dart';
import '/resources/themes/styles/design_constants.dart';
import 'package:nylo_framework/nylo_framework.dart';

class CheckoutDetailsPage extends NyStatefulWidget {
  static RouteView path = ("/address-form", (_) => CheckoutDetailsPage());
  
  final CustomerAddress? address;

  CheckoutDetailsPage({super.key, this.address})
      : super(child: () => _CheckoutDetailsPageState());
}

class _CheckoutDetailsPageState extends NyPage<CheckoutDetailsPage> {
  final _formKey = GlobalKey<FormState>();
  
  // Consolidated controllers for single address form
  final TextEditingController _tfFirstName = TextEditingController(),
      _tfLastName = TextEditingController(),
      _tfAddressLine = TextEditingController(),
      _tfCity = TextEditingController(),
      _tfPostalCode = TextEditingController(),
      _tfEmailAddress = TextEditingController(),
      _tfPhoneNumber = TextEditingController(),
      _tfAddressName = TextEditingController(); // Address name field

  CustomerCountry? _customerCountry;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _customerCountry = CustomerCountry(countryCode: 'LY', name: 'Libya');
    
    // Populate fields if editing existing address
    if (widget.address != null) {
      _populateFields();
    }
  }

  void _populateFields() {
    final address = widget.address!;
    _tfAddressName.text = address.name ?? '';
    _tfFirstName.text = address.firstName ?? '';
    _tfLastName.text = address.lastName ?? '';
    _tfAddressLine.text = address.addressLine ?? '';
    _tfCity.text = address.city ?? '';
    _tfPostalCode.text = address.postalCode ?? '';
    _tfEmailAddress.text = address.emailAddress ?? '';
    _tfPhoneNumber.text = address.phoneNumber ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFEF8F8),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          widget.address == null ? 'إضافة عنوان جديد' : 'تعديل العنوان',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Color(0xFF2E3A59)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              _buildFormCard(),
              SizedBox(height: 20),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: DesignConstants.cardDecoration(
        backgroundColor: Theme.of(context).cardColor,
        isDark: Theme.of(context).brightness == Brightness.dark,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Address Name Field (Optional, not sent to WooCommerce)
          TextFormField(
            controller: _tfAddressName,
            decoration: InputDecoration(
              labelText: 'اسم العنوان (اختياري)',
              hintText: 'مثل: المنزل، العمل، منزل الأهل',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
            validator: null, // Optional field
          ),
          SizedBox(height: 8),
          Text(
            'للمرجع الشخصي فقط - لن يتم إرساله مع الطلب',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
          SizedBox(height: 20),
          
          // Use CustomerAddressInput widget (the billing fool reference)
          CustomerAddressInput(
            txtControllerFirstName: _tfFirstName,
            txtControllerLastName: _tfLastName,
            txtControllerAddressLine: _tfAddressLine,
            txtControllerCity: _tfCity,
            txtControllerPostalCode: _tfPostalCode,
            txtControllerEmailAddress: _tfEmailAddress,
            txtControllerPhoneNumber: _tfPhoneNumber,
            customerCountry: _customerCountry,
            onTapCountry: () {}, // Libya is locked, no action needed
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveAddress,
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0xFFB76E79),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? CircularProgressIndicator(color: Colors.white)
            : Text(
                'حفظ العنوان',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  void _saveAddress() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      CustomerAddress address = CustomerAddress(
        addressId: widget.address?.addressId,
        name: _tfAddressName.text.trim().isEmpty ? null : _tfAddressName.text.trim(),
        firstName: _tfFirstName.text,
        lastName: _tfLastName.text,
        addressLine: _tfAddressLine.text,
        city: _tfCity.text,
        postalCode: _tfPostalCode.text,
        phoneNumber: _tfPhoneNumber.text,
        emailAddress: _tfEmailAddress.text,
        customerCountry: CustomerCountry(
          countryCode: 'LY',
          name: 'Libya',
        ),
        // Note: addressLabel and isDefault are NOT included (removed unwanted elements)
      );

      bool success;
      if (widget.address == null) {
        // Adding new address
        success = await CheckoutSession.getInstance.addAddress(address);
        if (!success) {
          // Handle 5-address limit
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن إضافة المزيد من العناوين. لقد وصلت إلى الحد الأقصى 5.'),
              backgroundColor: Colors.orange,
            ),
          );
          setState(() => _isLoading = false);
          return;
        }
      } else {
        // Updating existing address
        success = await CheckoutSession.getInstance.updateAddress(address);
      }

      if (success) {
        // CRITICAL FIX: Auto-select the newly saved/updated address
        if (address.addressId != null) {
          print('🔄 Auto-selecting newly saved address: ${address.firstName} ${address.lastName}');
          await CheckoutSession.getInstance.setSelectedAddress(address.addressId!);
          print('✅ Address auto-selected for checkout');
        }

        Navigator.pop(context, true); // Return success result
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.address == null ? 'تم حفظ العنوان بنجاح وتم تحديده للطلب' : 'تم تحديث العنوان بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حفظ العنوان'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء حفظ العنوان')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
