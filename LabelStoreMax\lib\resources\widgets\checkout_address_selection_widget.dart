//  Velvete Store
//
//  Created by <PERSON><PERSON>.
//  2025, Velvete Ltd. All rights reserved.
//
//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/checkout_session.dart';
import '/app/models/customer_address.dart';
import '/app/services/libyan_delivery_service.dart';
import '/resources/pages/checkout_details_page.dart';
import '/bootstrap/helpers.dart';

class CheckoutAddressSelectionWidget extends StatefulWidget {
  final CheckoutSession checkoutSession;
  final VoidCallback onAddressChanged;

  const CheckoutAddressSelectionWidget({
    super.key,
    required this.checkoutSession,
    required this.onAddressChanged,
  });

  @override
  _CheckoutAddressSelectionWidgetState createState() => _CheckoutAddressSelectionWidgetState();
}

class _CheckoutAddressSelectionWidgetState extends State<CheckoutAddressSelectionWidget> {
  bool _isAddressDropdownExpanded = false;
  List<CustomerAddress> _availableAddresses = [];
  bool _isLoadingAddresses = false;
  CustomerAddress? _selectedAddress;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
    _syncWithCheckoutSession();
  }

  /// Synchronize widget state with CheckoutSession selected address
  void _syncWithCheckoutSession() async {
    // Get the currently selected address from CheckoutSession
    CustomerAddress? sessionAddress = widget.checkoutSession.billingDetails?.billingAddress;

    if (sessionAddress != null && sessionAddress.addressId != null) {
      setState(() {
        _selectedAddress = sessionAddress;
      });
      print('✅ Widget synchronized with CheckoutSession address: ${sessionAddress.firstName} ${sessionAddress.lastName}');
    }
  }

  void _loadAddresses() async {
    setState(() => _isLoadingAddresses = true);
    
    _availableAddresses = CheckoutSession.getInstance.getAddresses();
    _selectedAddress = await CheckoutSession.getInstance.getDefaultAddress();
    
    setState(() => _isLoadingAddresses = false);
  }

  void _toggleAddressDropdown() async {
    if (!_isAddressDropdownExpanded) {
      // Load addresses when expanding
      setState(() {
        _isLoadingAddresses = true;
        _isAddressDropdownExpanded = true;
      });
      
      _loadAddresses();
    } else {
      // Collapse dropdown
      setState(() {
        _isAddressDropdownExpanded = false;
      });
    }
  }

  void _selectAddressFromDropdown(CustomerAddress address) async {
    print('🔄 Selecting address from dropdown: ${address.firstName} ${address.lastName}');

    await CheckoutSession.getInstance.setSelectedAddress(address.addressId!);

    // Update shipping cost for the selected city
    if (address.city != null && address.city!.isNotEmpty) {
      await LibyanDeliveryService().updateShippingCostForCity(address.city!);
    }

    // Synchronize local state with CheckoutSession
    setState(() {
      _selectedAddress = address;
      _isAddressDropdownExpanded = false;
    });

    print('✅ Address selected and synchronized: ${address.firstName} ${address.lastName}');

    // Notify parent to refresh
    widget.onAddressChanged();
  }

  void _navigateToAddressForm({CustomerAddress? editAddress}) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CheckoutDetailsPage(address: editAddress),
      ),
    );
    
    if (result == true) {
      // Refresh addresses after form completion
      _loadAddresses();
      widget.onAddressChanged();
    }
  }

  void _deleteAddressWithConfirmation(CustomerAddress address) async {
    // Show confirmation dialog
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف العنوان'),
        content: Text(_availableAddresses.length == 1 
            ? 'ليس لديك عنوان آخر للاستخدام، هل أنت متأكد من هذا الإجراء؟'
            : 'هل أنت متأكد من حذف هذا العنوان؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirm == true) {
      bool success = await CheckoutSession.getInstance.deleteAddress(address.addressId!);
      if (success) {
        _loadAddresses();
        widget.onAddressChanged();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حذف العنوان بنجاح')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: ThemeColor.get(context).primaryAccent,
                size: 24,
              ),
              SizedBox(width: 12),
              Text(
                'عنوان التوصيل',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: ThemeColor.get(context).textPrimary,
                ),
              ),
              Spacer(),
              TextButton(
                onPressed: _toggleAddressDropdown,
                child: Text(
                  _isAddressDropdownExpanded ? 'إغلاق' : 'اختر عنوانًا',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 16),

          // Address content
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ThemeColor.get(context).surfaceElevated,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: ThemeColor.get(context).borderPrimary,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Selected address display or prompt
                _buildSelectedAddressDisplay(),
                
                // In-page dropdown widget
                if (_isAddressDropdownExpanded) ...[
                  SizedBox(height: 16),
                  _buildAddressDropdownWidget(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedAddressDisplay() {
    if (_selectedAddress != null) {
      final address = _selectedAddress!;
      String displayName = address.name?.isNotEmpty == true
          ? address.name!
          : 'العنوان المحدد';

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            displayName,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeColor.get(context).textPrimary,
            ),
          ),
          SizedBox(height: 4),
          Text(
            '${address.firstName} ${address.lastName}',
            style: TextStyle(
              fontSize: 14,
              color: ThemeColor.get(context).textSecondary,
            ),
          ),
          Text(
            '${address.addressLine}, ${address.city}',
            style: TextStyle(
              fontSize: 13,
              color: ThemeColor.get(context).textSecondary,
            ),
          ),
          if (address.phoneNumber?.isNotEmpty == true)
            Text(
              'الهاتف: ${address.phoneNumber}',
              style: TextStyle(
                fontSize: 13,
                color: ThemeColor.get(context).textSecondary,
              ),
            ),
        ],
      );
    }

    return Text(
      'لم يتم تحديد عنوان',
      style: TextStyle(
        fontSize: 14,
        color: Colors.grey[600],
        fontStyle: FontStyle.italic,
      ),
    );
  }

  Widget _buildAddressDropdownWidget() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Color(0xFFE0E0E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: _isLoadingAddresses
          ? Container(
              height: 100,
              child: Center(child: CircularProgressIndicator()),
            )
          : _availableAddresses.isEmpty
              ? _buildEmptyAddressState()
              : _buildAddressListDropdown(),
    );
  }

  Widget _buildEmptyAddressState() {
    return Container(
      padding: EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(Icons.location_off, size: 48, color: Colors.grey[400]),
          SizedBox(height: 16),
          Text(
            'لم تقم بإنشاء عنوان بعد',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2E3A59),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'أنشئ عنوانًا جديدًا الآن',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _navigateToAddressForm(),
            icon: Icon(Icons.add_location, color: Colors.white),
            label: Text(
              'إنشاء عنوان جديد',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFFB76E79),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressListDropdown() {
    return Container(
      constraints: BoxConstraints(maxHeight: 300),
      child: Column(
        children: [
          // Add new address button
          Container(
            width: double.infinity,
            margin: EdgeInsets.all(12),
            child: ElevatedButton.icon(
              onPressed: () => _navigateToAddressForm(),
              icon: Icon(Icons.add, color: Colors.white, size: 18),
              label: Text(
                'إضافة عنوان جديد',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFFB76E79),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),

          // Address list
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _availableAddresses.length,
              itemBuilder: (context, index) {
                final address = _availableAddresses[index];
                return _buildAddressCardInDropdown(address, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressCardInDropdown(CustomerAddress address, int index) {
    bool isSelected = _selectedAddress?.addressId == address.addressId;
    String displayName = address.name?.isNotEmpty == true
        ? address.name!
        : 'العنوان ${index + 1}';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? Color(0xFFB76E79).withValues(alpha: 0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? Color(0xFFB76E79) : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _selectAddressFromDropdown(address),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: isSelected ? Color(0xFFB76E79) : Colors.grey.shade600,
                      size: 18,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        displayName,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isSelected ? Color(0xFFB76E79) : Color(0xFF2E3A59),
                        ),
                      ),
                    ),
                    // Edit and Delete buttons
                    IconButton(
                      onPressed: () => _navigateToAddressForm(editAddress: address),
                      icon: Icon(Icons.edit, size: 18, color: Colors.blue),
                      tooltip: 'تعديل',
                      constraints: BoxConstraints(minWidth: 32, minHeight: 32),
                      padding: EdgeInsets.all(4),
                    ),
                    IconButton(
                      onPressed: () => _deleteAddressWithConfirmation(address),
                      icon: Icon(Icons.delete, size: 18, color: Colors.red),
                      tooltip: 'حذف',
                      constraints: BoxConstraints(minWidth: 32, minHeight: 32),
                      padding: EdgeInsets.all(4),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Text(
                  '${address.firstName ?? ''} ${address.lastName ?? ''}'.trim(),
                  style: TextStyle(fontSize: 12, color: Color(0xFF2E3A59)),
                ),
                Text(
                  '${address.addressLine ?? ''}, ${address.city ?? ''}',
                  style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (address.emailAddress?.isNotEmpty == true)
                  Text(
                    'البريد: ${address.emailAddress}',
                    style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
                  ),
                if (address.phoneNumber?.isNotEmpty == true)
                  Text(
                    'الهاتف: ${address.phoneNumber}',
                    style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
                  ),
                Text(
                  'البلد: Libya',
                  style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
